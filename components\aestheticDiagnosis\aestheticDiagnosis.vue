<template>

  <view class="w-full max-w-[750rpx] min-h-screen flex flex-col items-center justify-between mx-auto pt-0">
    <!-- 上半部分：图片展示 -->
    <view style="transition: all 0.3s;" class="w-full flex-1 flex flex-col items-center"
      :class="!props.compareFlag ? 'justify-center' : 'mt-24'">

      <img ref="beforeimgRef" v-if="!props.compareFlag" :src="props.peopleImg" class="w-full object-cover"
        mode="aspectFill">
      <ImgCompare v-else :before="props.peopleImg" :after="props.peopleImg" :height="beforeImgHeight" />
      <template v-if="props.modelValue">
        <!-- 标签标记 -->
        <view class="absolute left-[25%] top-[30%]">
          <view class="tag">太阳穴凹陷</view>
        </view>
        <view class="absolute left-[45%] top-[20%]">
          <view class="tag">眉骨凸显</view>
        </view>
        <view class="absolute left-[45%] top-[35%]">
          <view class="tag">眼距过大</view>
        </view>
        <view class="absolute left-[25%] top-[45%]">
          <view class="tag">去卧蚕</view>
        </view>

        <!-- 下载按钮 -->
        <button class="absolute bottom-4 right-4 w-8 h-8 text-gray-600 text-center p-0"
          style="line-height: 2rem;border-radius: 50%;">
          <!-- <uni-icons type="download" size="24" color="#666"></uni-icons> -->
        </button>
      </template>

    </view>
    <view v-if="!!props.activeReport" class="popup-container" :class="popupVisible ? 'slide-up' : 'slide-down'"
      @click.stop>
      <!-- <view class="popup-header">
          <text class="popup-title"></text>
          <uni-icons type="closeempty" size="24" color="#666" @click="closePopup"></uni-icons>
        </view> -->

      <view v-if="props.activeReport == '诊断报告'" class=" ">
        <button @click="handlePreview()" style="margin: 0 auto;padding: 0;line-height: 30rpx;"
          class="bg-[#FF8F9C] text-white text-xs rounded  w-10 py-1 font-medium">
          <u-icon v-if="!props.compareFlag" size="24" color="#fff" name="arrow-down"></u-icon>
          <u-icon v-else size="24" color="#fff" name="arrow-up"></u-icon>
        </button>
        <view class="popup-content py-1 px-6 m-2 ">
          <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">玻尿酸</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            推荐理由：太阳穴和面颊的话都是适合注射一些支撑塑形性比较强的大分子玻尿酸，改善面部线条。通常维持3-6个月。
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">肉毒素</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            推荐理由：减少动态皱纹（如鱼尾纹、抬头纹、眉间纹），效果通常维持3-6个月。
          </text>
        </view>
      </view>
      <template v-if="props.activeReport == '美学方案'">
        <button @click="handlePreview()" style="margin: 0 auto;padding: 0;line-height: 30rpx;"
          class="bg-[#FF8F9C] text-white text-xs rounded  w-10 py-1 font-medium">
          <u-icon v-if="!props.compareFlag" size="24" color="#fff" name="arrow-down"></u-icon>
          <u-icon v-else size="24" color="#fff" name="arrow-up"></u-icon>
        </button>
        <view class="popup-content py-1 px-6 m-2 " v-if="!props.compareFlag">
          <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">玻尿酸</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            推荐理由：太阳穴和面颊的话都是适合注射一些支撑塑形性比较强的大分子玻尿酸，改善面部线条。通常维持3-6个月。
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">肉毒素</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            推荐理由：减少动态皱纹（如鱼尾纹、抬头纹、眉间纹），效果通常维持3-6个月。
          </text>
        </view>
      </template>

      <scroll-view v-if="props.activeReport == '专属推荐'" class="popup-content" scroll-x style="white-space: nowrap;">
        <view class="flex flex-nowrap" style="width: max-content;">
          <view class="flex items-center mx-2 p-2 bg-[#F8F8F8]" v-for="(item, index) in recommendedItems"
            @click="gotoOrgList" :key="index">
            <div class="flex flex-col flex-1">
              <div class="text-[14px] text-[#222] leading-tight" style="font-family: 'PingFang SC', Arial;">{{
                item.title }}</div>
              <div class="text-[12px] text-[#999] mt-1" style="font-family: 'PingFang SC', Arial;">{{
                item.category }}
                <span>{{ item.location }}</span>
              </div>
            </div>

            <img :src="item.imgSrc" :alt="item.alt" class="w-14 h-14 rounded-lg object-cover ml-3" />
          </view>
        </view>
      </scroll-view>

    </view>

    <!-- 底部导航栏2 -->
    <view v-show="props.activeReport" style="z-index: 5;"
      class="fixed bottom-0 left-0 w-full bg-[#fff] border-t border-[#f0f0f0] transition-transform duration-300 transform"
      :class="{ 'translate-y-0': props.activeReport, 'translate-y-full': !props.activeReport }">
      <view class="flex justify-around items-center h-16 px-4">
        <view v-for="item in reports" style="font-size: 28rpx;border-color: #F39196;" class="font-semi"
          :class="props.activeReport == item ? 'font-semibold border-b-2' : ''" @click="selectIcon2(item)">{{ item
          }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import ImgCompare from '@/components/imgCompare.vue';
import { ref, computed, watch, onMounted } from 'vue';
import { callAiPolling } from "@/Api/index.js"
const props = defineProps({
  compareFlag: {
    type: Boolean,
    default: false
  },
  peopleImg: {
    type: String,
    default: ''
  },

  buttonIndex: {
    type: Number,
    default: -1
  },
  activeReport: {
    type: String,
    default: ''
  },
  icons: {
    type: Array,
    default: []
  },
  reports: {
    type: Array,
    default: []
  },

});
// watch(props.buttonIndex, (newval) => {
//   console.log('newval', newval);
//   if (newval !== undefined && icons.value[newval]?.reports) {
//     let label = icons.value[newval].reports[0];
//     selectIcon2(label);
//   }
// })
onMounted(() => {
  let label = props.icons[0].reports[0];

  selectIcon2(label);

})
const beforeImgHeight = ref(0) // 原图高度
const handlePreview = () => {
  if (beforeimgRef.value) {
    beforeImgHeight.value = beforeimgRef.value.height
  }
  previewFlag.value = !previewFlag.value
}
let operationId = uni.getStorageSync('operationId')
if (operationId) {
  let timer = setInterval(async () => {

    let { data } = await callAiPolling({ operationId })
    let status = data.data.every(item => item.aiStatus == '1')
    if (status) {
      clearInterval(timer)
    }
  }, 3000)
}
const popupVisible = ref(false)
const selectIcon2 = (item) => {
  // activeReport.value = item;
  emit('update:activeReport', item)
  setTimeout(() => {
    popupVisible.value = true;
  }, 50);
}

const gotoOrgList = () => {
  uni.navigateTo({
    url: '/pages/orgList/index'
  })
}
// 推荐项目数据
const recommendedItems = ref([
  {
    imgSrc: '/static/imgs/ject1.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "明星医师方案",
    category: "玻尿酸+肉毒素+光子嫩肤",
  },
  {
    imgSrc: '/static/imgs/ject2.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "黄金标准方案",
    category: "玻尿酸+肉毒素",
  },
  {
    imgSrc: '/static/imgs/ject3.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "经典臻选方案",
    category: "玻尿酸",
  },
]);


const emit = defineEmits(['update:activeReport']);

watch(() => props.activeReport, (newVal, oldVal) => {
  console.log('子组件:', newVal);
}, { deep: true });
</script>

<style lang="scss" scoped>
/* 标签样式 */
.tag {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

/* 弹窗样式 */
.popup-overlay {

  width: 100%;
  height: 100%;
  // background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.popup-container {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 4;
  width: 100%;
  height: max-content;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  border-top-left-radius: 5rpx;
  border-top-right-radius: 5rpx;
  padding: 20rpx 0 5rem;
  transform: translateY(100%);
  transition: all 0.3s;
}

.popup-container.slide-up {
  transform: translateY(0);
}

.popup-container.slide-down {
  transform: translateY(100%);
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
}

.popup-content {
  width: 100%;
  overflow-y: auto;
}

/* 横向滚动样式 */
scroll-view ::v-deep {
  .flex {
    flex-wrap: nowrap;
  }
}

/* 底部标签栏样式 */
.bottom-tab {
  position: relative;
}

.bottom-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 4rpx;
  background-color: #ff4d4f;
}

.inactive-tab {
  color: #999;
}
</style>